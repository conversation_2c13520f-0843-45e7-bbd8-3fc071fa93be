openapi: 3.0.3
info:
  title: 教学助手系统 - 时间选择自由化更新
  description: |
    排课管理系统时间选择功能更新说明
    
    ## 更新内容
    - 前端新增排课弹框支持任意分钟选择（之前仅支持整点和半点）
    - 后端接口已完全支持任意时间精度（精确到秒）
    - 时间冲突检查支持任意时间段的冲突检测
    
    ## 影响的接口
    本次更新主要影响前端用户体验，后端接口保持兼容性，无需修改。
    
  version: 1.1.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  /principal/courses:
    post:
      tags:
        - 课程管理
      summary: 创建课程（校长端）
      description: |
        创建新的课程安排
        
        ## 时间选择更新
        - **startTime** 和 **endTime** 字段现在支持任意分钟选择
        - 格式：HH:mm:ss（如：08:15:00, 14:45:00）
        - 前端界面已移除分钟限制，用户可自由选择时间
        - 后端会进行精确的时间冲突检查
        
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - teacherId
                - studentId
                - classroomId
                - courseDate
                - startTime
                - endTime
                - price
              properties:
                teacherId:
                  type: integer
                  format: int64
                  description: 教师ID
                  example: 1
                studentId:
                  type: integer
                  format: int64
                  description: 学生ID
                  example: 1
                classroomId:
                  type: integer
                  format: int64
                  description: 教室ID
                  example: 1
                courseDate:
                  type: string
                  format: date
                  description: 上课日期
                  example: "2024-01-15"
                startTime:
                  type: string
                  format: time
                  description: |
                    开始时间（支持任意分钟）
                    格式：HH:mm:ss
                  example: "08:15:00"
                endTime:
                  type: string
                  format: time
                  description: |
                    结束时间（支持任意分钟）
                    格式：HH:mm:ss
                  example: "09:45:00"
                price:
                  type: number
                  format: decimal
                  description: 课程价格
                  example: 100.00
                gradeLevel:
                  type: string
                  description: 年级
                  example: "高一"
            examples:
              flexible_time_example:
                summary: 灵活时间安排示例
                description: 展示如何使用任意分钟进行排课
                value:
                  teacherId: 1
                  studentId: 1
                  classroomId: 1
                  courseDate: "2024-01-15"
                  startTime: "08:15:00"
                  endTime: "09:45:00"
                  price: 100.00
                  gradeLevel: "高一"
              traditional_time_example:
                summary: 传统时间安排示例
                description: 传统的整点/半点时间安排仍然支持
                value:
                  teacherId: 1
                  studentId: 1
                  classroomId: 1
                  courseDate: "2024-01-15"
                  startTime: "08:00:00"
                  endTime: "09:30:00"
                  price: 100.00
                  gradeLevel: "高一"
      responses:
        '200':
          description: 课程创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "课程创建成功"
                  data:
                    $ref: '#/components/schemas/Course'
        '400':
          description: 参数错误或时间冲突
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 400
                  message:
                    type: string
                    example: "该时间段已有课程安排"
                  data:
                    type: object
                    nullable: true

  /principal/courses/available-slots:
    get:
      tags:
        - 课程管理
      summary: 获取空闲时段（校长端）
      description: |
        获取指定教师和教室在指定日期范围内的空闲时段
        
        ## 注意事项
        - 返回的空闲时段以30分钟为间隔（性能考虑）
        - 用户可以在这些时段基础上选择任意开始和结束时间
        - 实际排课时会进行精确的时间冲突检查
        
      parameters:
        - name: teacherId
          in: query
          required: true
          schema:
            type: integer
            format: int64
          description: 教师ID
          example: 1
        - name: classroomId
          in: query
          required: true
          schema:
            type: integer
            format: int64
          description: 教室ID
          example: 1
        - name: startDate
          in: query
          required: true
          schema:
            type: string
            format: date
          description: 开始日期
          example: "2024-01-15"
        - name: endDate
          in: query
          required: true
          schema:
            type: string
            format: date
          description: 结束日期
          example: "2024-01-21"
        - name: scheduleMode
          in: query
          required: false
          schema:
            type: string
            enum: [standard, extended]
            default: extended
          description: |
            排课模式
            - standard: 标准模式（8:00-12:00, 14:00-18:00）
            - extended: 24小时模式（0:00-24:00）
          example: "extended"
      responses:
        '200':
          description: 获取空闲时段成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取空闲时段成功"
                  data:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        $ref: '#/components/schemas/TimeSlot'
                    example:
                      "2024-01-15":
                        - startTime: "08:00:00"
                          endTime: "08:30:00"
                          available: true
                        - startTime: "08:30:00"
                          endTime: "09:00:00"
                          available: true

components:
  schemas:
    Course:
      type: object
      properties:
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1
        schoolId:
          type: integer
          format: int64
          description: 学校ID
          example: 1
        teacherId:
          type: integer
          format: int64
          description: 教师ID
          example: 1
        studentId:
          type: integer
          format: int64
          description: 学生ID
          example: 1
        classroomId:
          type: integer
          format: int64
          description: 教室ID
          example: 1
        courseDate:
          type: string
          format: date
          description: 上课日期
          example: "2024-01-15"
        startTime:
          type: string
          format: time
          description: |
            开始时间（支持任意分钟）
            格式：HH:mm:ss
          example: "08:15:00"
        endTime:
          type: string
          format: time
          description: |
            结束时间（支持任意分钟）
            格式：HH:mm:ss
          example: "09:45:00"
        price:
          type: number
          format: decimal
          description: 课程价格
          example: 100.00
        gradeLevel:
          type: string
          description: 年级
          example: "高一"
        status:
          type: string
          enum: [scheduled, completed, cancelled]
          description: 课程状态
          example: "scheduled"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-01-15T10:30:00"
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
          example: "2024-01-15T10:30:00"
        teacher:
          type: object
          description: 教师信息
          properties:
            userId:
              type: integer
              format: int64
              example: 1
            realName:
              type: string
              example: "张老师"
            phone:
              type: string
              example: "13800138000"
            email:
              type: string
              example: "<EMAIL>"
        student:
          type: object
          description: 学生信息
          properties:
            studentId:
              type: integer
              format: int64
              example: 1
            name:
              type: string
              example: "李同学"
            contactPhone:
              type: string
              example: "13900139000"
        classroom:
          type: object
          description: 教室信息
          properties:
            classroomId:
              type: integer
              format: int64
              example: 1
            name:
              type: string
              example: "A101"
            floor:
              type: integer
              example: 1
            type:
              type: string
              example: "普通教室"

    TimeSlot:
      type: object
      description: 时间段信息
      properties:
        startTime:
          type: string
          format: time
          description: 开始时间
          example: "08:00:00"
        endTime:
          type: string
          format: time
          description: 结束时间
          example: "08:30:00"
        available:
          type: boolean
          description: 是否可用
          example: true

tags:
  - name: 课程管理
    description: 课程相关的API接口
